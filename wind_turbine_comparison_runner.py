#!/usr/bin/env python3
"""
Wind Turbine Dataset Comparison Analysis Runner

This script provides a command-line interface to run the wind turbine comparison analysis.
It can be used as an alternative to the Jupyter notebook for automated processing.

Usage:
    python wind_turbine_comparison_runner.py --csv_path "path/to/csv" --pickle_path "path/to/pickle"
"""

import argparse
import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime
import warnings

warnings.filterwarnings('ignore')

def setup_paths():
    """Setup default file paths"""
    return {
        'csv_default': "rapports_mensuel/2025-02/Production Detail 2025-02.csv",
        'pickle_alternatives': [
            "../DATA/results/2025-02.pkl",
            "DATA/results/2025-02.pkl", 
            "../../DATA/results/2025-02.pkl",
            "../../../DATA/results/2025-02.pkl"
        ]
    }

def find_pickle_file(custom_path=None):
    """Find the pickle file from multiple possible locations"""
    paths = setup_paths()
    
    if custom_path and os.path.exists(custom_path):
        return custom_path
    
    for path in paths['pickle_alternatives']:
        if os.path.exists(path):
            print(f"Found pickle file at: {path}")
            return path
    
    return None

def load_and_validate_data(csv_path, pickle_path):
    """Load and validate both datasets"""
    print("Loading datasets...")
    
    # Load CSV
    try:
        df_csv = pd.read_csv(csv_path, encoding='utf-8')
        df_csv['Timestamp'] = pd.to_datetime(df_csv['Timestamp'], format='%d/%m/%Y %H:%M:%S')
        print(f"CSV loaded: {df_csv.shape}")
    except Exception as e:
        print(f"Error loading CSV: {e}")
        return None, None
    
    # Load pickle
    try:
        df_pickle = pd.read_pickle(pickle_path)
        if 'TimeStamp' in df_pickle.columns:
            if not pd.api.types.is_datetime64_any_dtype(df_pickle['TimeStamp']):
                df_pickle['TimeStamp'] = pd.to_datetime(df_pickle['TimeStamp'])
        print(f"Pickle loaded: {df_pickle.shape}")
    except Exception as e:
        print(f"Error loading pickle: {e}")
        return df_csv, None
    
    return df_csv, df_pickle

def create_turbine_mapping():
    """Create turbine ID mapping functions"""
    BASE_STATION_ID = 2307404
    
    def turbine_to_station(turbine_name):
        try:
            parts = turbine_name.split('_')
            if len(parts) >= 2:
                turbine_num = int(parts[1])
            else:
                turbine_num = int(turbine_name.replace('S', '').split('_')[0])
            return str(BASE_STATION_ID + turbine_num)
        except:
            return None
    
    def station_to_turbine(station_id):
        try:
            station_num = int(station_id)
            turbine_num = station_num - BASE_STATION_ID
            return f"S{turbine_num:03d}_12"
        except:
            return None
    
    return turbine_to_station, station_to_turbine

def calculate_derived_columns(df):
    """Calculate derived columns for Dataset 2"""
    df_calc = df.copy()
    
    required_cols = ['wtc_kWG1TotE_accum', 'ELX', 'ELNX', 'EL_PowerRed', 'EL_2006', 'EL_Misassigned']
    for col in required_cols:
        if col not in df_calc.columns:
            df_calc[col] = 0
    
    df_calc['PEP_calculated'] = (
        df_calc['wtc_kWG1TotE_accum'] + df_calc['ELX'] + df_calc['ELNX'] + 
        df_calc['EL_PowerRed'] + df_calc['EL_2006'] + df_calc['EL_Misassigned']
    )
    
    df_calc['EL_calculated'] = (
        df_calc['ELNX'] + df_calc['EL_PowerRed'] + 
        df_calc['EL_2006'] + df_calc['EL_Misassigned']
    )
    
    df_calc['ELX_calculated'] = df_calc['ELX'] - df_calc['EL_Misassigned']
    
    return df_calc

def perform_comparison(df1, df2):
    """Perform the main comparison analysis"""
    print("Performing comparison analysis...")
    
    # Create mapping functions
    turbine_to_station, station_to_turbine = create_turbine_mapping()
    
    # Add normalized IDs
    df1['StationId_normalized'] = df1['Turbine Name'].apply(turbine_to_station)
    df2['TurbineName_normalized'] = df2['StationId'].apply(station_to_turbine)
    
    # Calculate derived columns for dataset 2
    df2_calc = calculate_derived_columns(df2)
    
    # Prepare for merging
    df1_prep = df1[['Turbine Name', 'Timestamp', 'EP', 'PEP', 'EL', 'ELX', 'StationId_normalized']].copy()
    df1_prep = df1_prep.dropna(subset=['StationId_normalized'])
    df1_prep.rename(columns={'Timestamp': 'timestamp_aligned', 'StationId_normalized': 'station_id_aligned'}, inplace=True)
    
    df2_prep = df2_calc[['StationId', 'TimeStamp', 'wtc_kWG1TotE_accum', 'PEP_calculated', 'EL_calculated', 'ELX_calculated']].copy()
    df2_prep.rename(columns={'TimeStamp': 'timestamp_aligned', 'StationId': 'station_id_aligned'}, inplace=True)
    
    # Round timestamps
    df1_prep['timestamp_aligned'] = df1_prep['timestamp_aligned'].dt.round('10min')
    df2_prep['timestamp_aligned'] = df2_prep['timestamp_aligned'].dt.round('10min')
    
    # Merge
    merged = pd.merge(df1_prep, df2_prep, on=['station_id_aligned', 'timestamp_aligned'], how='inner', suffixes=('_ds1', '_ds2'))
    
    print(f"Merged data shape: {merged.shape}")
    
    # Calculate differences
    comparison_pairs = {
        'EP': ('EP', 'wtc_kWG1TotE_accum'),
        'PEP': ('PEP', 'PEP_calculated'),
        'EL': ('EL', 'EL_calculated'),
        'ELX': ('ELX', 'ELX_calculated')
    }
    
    for metric, (col1, col2) in comparison_pairs.items():
        merged[f'{metric}_abs_diff'] = merged[col1] - merged[col2]
        denominator = merged[col2].replace(0, np.nan)
        merged[f'{metric}_pct_diff'] = ((merged[col1] - merged[col2]) / denominator) * 100
    
    return merged

def export_results(merged_data, output_dir="analysis_results"):
    """Export analysis results"""
    os.makedirs(output_dir, exist_ok=True)
    timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Export main results
    filename = f"{output_dir}/comparison_results_{timestamp_str}.csv"
    merged_data.to_csv(filename, index=False)
    print(f"Results exported to: {filename}")
    
    # Export summary statistics
    diff_cols = [col for col in merged_data.columns if '_abs_diff' in col]
    summary_stats = merged_data[diff_cols].describe()
    
    summary_filename = f"{output_dir}/summary_statistics_{timestamp_str}.csv"
    summary_stats.to_csv(summary_filename)
    print(f"Summary statistics exported to: {summary_filename}")
    
    return filename, summary_filename

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Wind Turbine Dataset Comparison Analysis')
    parser.add_argument('--csv_path', type=str, help='Path to CSV file')
    parser.add_argument('--pickle_path', type=str, help='Path to pickle file')
    parser.add_argument('--output_dir', type=str, default='analysis_results', help='Output directory')
    
    args = parser.parse_args()
    
    # Setup paths
    paths = setup_paths()
    csv_path = args.csv_path or paths['csv_default']
    pickle_path = args.pickle_path or find_pickle_file()
    
    if not os.path.exists(csv_path):
        print(f"Error: CSV file not found at {csv_path}")
        sys.exit(1)
    
    if not pickle_path or not os.path.exists(pickle_path):
        print("Error: Pickle file not found. Please specify --pickle_path")
        sys.exit(1)
    
    print(f"Using CSV: {csv_path}")
    print(f"Using Pickle: {pickle_path}")
    
    # Load data
    df_csv, df_pickle = load_and_validate_data(csv_path, pickle_path)
    
    if df_csv is None or df_pickle is None:
        print("Error: Could not load one or both datasets")
        sys.exit(1)
    
    # Perform comparison
    try:
        results = perform_comparison(df_csv, df_pickle)
        
        # Export results
        result_file, summary_file = export_results(results, args.output_dir)
        
        print("\n=== Analysis Complete ===")
        print(f"Results saved to: {args.output_dir}/")
        print(f"Main results: {result_file}")
        print(f"Summary statistics: {summary_file}")
        
    except Exception as e:
        print(f"Error during analysis: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
