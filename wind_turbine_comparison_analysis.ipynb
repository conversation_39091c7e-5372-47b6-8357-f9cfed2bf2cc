# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import os
from datetime import datetime

# Configure display options
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)
warnings.filterwarnings('ignore')

# Set plotting style
# plt.style.use('default')
# sns.set_palette("husl")


period = "2025-02"
# File paths configuration
CSV_FILE_PATH = f"rapports_mensuel/{period}/Production Detail {period}.csv"
pickle_path = f"../DATA/results/{period}.pkl"  # Adjust this path as needed

# Check file existence
print("Checking file availability...")
csv_exists = os.path.exists(CSV_FILE_PATH)
pickle_exists = os.path.exists(pickle_path)

print(f"CSV file exists: {csv_exists}")
print(f"Pickle file exists: {pickle_exists}")

if not csv_exists:
    print(f"ERROR: CSV file not found at {CSV_FILE_PATH}")
if not pickle_exists:
    print(f"ERROR: Pickle file not found at {pickle_path}")


def load_csv_data(file_path):
    """Load and preprocess CSV data with proper datetime parsing"""
    try:
        print(f"Loading CSV data from: {file_path}")

        # Load CSV with proper datetime parsing and decimal separator
        df = pd.read_csv(file_path, encoding="utf-8", decimal=".", sep=",")

        print(f"CSV loaded successfully. Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")

        # Convert timestamp to datetime
        df["Timestamp"] = pd.to_datetime(df["Timestamp"], format="%d/%m/%Y %H:%M:%S")

        # Handle missing values
        print("Missing values per column:")
        missing_counts = df.isnull().sum()
        for col, count in missing_counts.items():
            if count > 0:
                print(f"  {col}: {count}")

        # Data validation
        print(f"Date range: {df['Timestamp'].min()} to {df['Timestamp'].max()}")
        print(f"Unique turbines: {df['Turbine Name'].nunique()}")

        return df

    except Exception as e:
        print(f"Error loading CSV data: {e}")
        return None


def load_pickle_data(file_path):
    """Load and preprocess pickle data"""
    try:
        print(f"Loading pickle data from: {file_path}")

        # Load pickle file
        df = pd.read_pickle(file_path)

        print(f"Pickle loaded successfully. Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")

        # Convert timestamp to datetime if needed
        if "TimeStamp" in df.columns:
            if not pd.api.types.is_datetime64_any_dtype(df["TimeStamp"]):
                df["TimeStamp"] = pd.to_datetime(df["TimeStamp"])

        # Handle missing values
        print("Missing values per column:")
        missing_counts = df.isnull().sum()
        for col, count in missing_counts.items():
            if count > 0:
                print(f"  {col}: {count}")

        # Data validation
        if "TimeStamp" in df.columns:
            print(f"Date range: {df['TimeStamp'].min()} to {df['TimeStamp'].max()}")
        if "StationId" in df.columns:
            print(f"Unique stations: {df['StationId'].nunique()}")

        return df

    except Exception as e:
        print(f"Error loading pickle data: {e}")
        return None


# Load datasets
dataset1 = None
dataset2 = None

if csv_exists:
    dataset1 = load_csv_data(CSV_FILE_PATH)

if pickle_exists:
    dataset2 = load_pickle_data(pickle_path)

# Display sample data from both datasets
if dataset1 is not None:
    print("=== Dataset 1 (CSV) Sample ===")
    print(dataset1.head())
    print("\nDataset 1 Info:")
    print(dataset1.info())

if dataset2 is not None:
    print("\n=== Dataset 2 (Pickle) Sample ===")
    print(dataset2.head())
    print("\nDataset 2 Info:")
    print(dataset2.info())

def calculate_derived_columns(df):
    """Calculate new derived columns for Dataset 2 to match Dataset 1 structure"""
    if df is None:
        print("Dataset 2 is None, skipping column calculations")
        return None
    
    df_calc = df.copy()
    
    print("Calculating derived columns for Dataset 2...")
    
    # Required columns for calculations
    required_cols = ['wtc_kWG1TotE_accum', 'ELX', 'ELNX', 'EL_PowerRed', 'EL_2006', 'EL_Misassigned']
    missing_cols = [col for col in required_cols if col not in df_calc.columns]
    
    if missing_cols:
        print(f"Warning: Missing columns for calculations: {missing_cols}")
        # Fill missing columns with zeros
        for col in missing_cols:
            df_calc[col] = 0
            print(f"  Added missing column '{col}' with zeros")
    
    # Calculate PEP_calculated
    df_calc['PEP_calculated'] = (
        df_calc['wtc_kWG1TotE_accum'] + 
        df_calc['ELX'] + 
        df_calc['ELNX'] + 
        df_calc['EL_PowerRed'] + 
        df_calc['EL_2006'] + 
        df_calc['EL_Misassigned']
    )
    
    # Calculate ELNX_calculated
    df_calc['ELNX_calculated'] = (
        df_calc['ELNX'] + 
        df_calc['EL_PowerRed'] + 
        df_calc['EL_2006'] + 
        df_calc['EL_Misassigned']
    )
    
    # Calculate ELX_calculated (replaces original ELX)
    df_calc['ELX_calculated'] = df_calc['ELX'] - df_calc['EL_Misassigned']
    
    print("Derived columns calculated successfully:")
    print(f"  - PEP_calculated: {df_calc['PEP_calculated'].describe()}")
    print(f"  - ELNX_calculated: {df_calc['ELNX_calculated'].describe()}")
    print(f"  - ELX_calculated: {df_calc['ELX_calculated'].describe()}")
    
    return df_calc

# Apply calculations to Dataset 2
if dataset2 is not None:
    dataset2 = calculate_derived_columns(dataset2)
    print(f"\nDataset 2 after calculations shape: {dataset2.shape}")
else:
    dataset2 = None
    print("Dataset 2 not available for calculations")

def create_column_mapping():
    """Define column correspondence between datasets"""
    return {
        "turbine_id": ("TurbineNum", "TurbineNum"),
        "timestamp": ("Timestamp", "TimeStamp"),
        "ep": ("EP", "wtc_kWG1TotE_accum"),
        "pep": ("PEP", "PEP_calculated"),
        "el": ("EL", "ELNX_calculated"),
        "elx": ("ELX", "ELX_calculated"),
    }


def normalize_turbine_ids(dataset1, dataset2):
    """
    Add normalized turbine IDs to both datasets using direct column manipulation.
    """
    if dataset1 is not None and isinstance(dataset1, pd.DataFrame):
        id_series = dataset1["Turbine Name"].str.split("_").str[0].str.replace("S", "")
        dataset1["TurbineNum"] = pd.to_numeric(id_series, errors="coerce")

        print(
            f"Dataset 1 - Added normalized IDs. Unique stations: {dataset1['TurbineNum'].nunique()}"
        )

    if dataset2 is not None and isinstance(dataset2, pd.DataFrame):
        # Add numeric turbine ID by subtracting the base offset
        dataset2["TurbineNum"] = dataset2["StationId"] - 2307404

        print(
            f"Dataset 2 - Added normalized IDs. Unique turbines: {dataset2['TurbineNum'].nunique()}"
        )

    return dataset1, dataset2


# --- Main execution block ---

# Create column mapping
column_mapping = create_column_mapping()
print("Column mapping created:")
for key, (col1, col2) in column_mapping.items():
    print(f"  {key}: {col1} <-> {col2}")

print("-" * 30)  # Separator for clarity

# Normalize turbine IDs
# This check ensures the variables exist and are not None before proceeding
if ("dataset1" in locals() and dataset1 is not None) or (
    "dataset2_calculated" in locals() and dataset2 is not None
):
    dataset1, dataset2 = normalize_turbine_ids(dataset1, dataset2)
    print("-" * 30)
    print("\nNormalized dataset1 head:")
    print(dataset1.head())
    print("\nNormalized dataset2 head:")
    print(dataset2.head())
else:
    dataset1, dataset2 = None, None
    print("No datasets were available for normalization.")


def align_datasets_temporally(df1, df2, column_mapping):
    """Align datasets by timestamp and turbine ID"""

    if df1 is None or df2 is None:
        print("One or both datasets are None, cannot perform temporal alignment")
        return None, None, None

    print("Performing temporal alignment...")

    # Prepare dataset 1 for merging
    df1_prep = df1[
        [
            "Turbine Name",
            "Timestamp",
            "EP",
            "PEP",
            "EL",
            "ELX",
            "TurbineNum",
        ]
    ].copy()
    df1_prep = df1_prep.dropna(subset=["TurbineNum"])

    # Prepare dataset 2 for merging
    df2_prep = df2[
        [
            "StationId",
            "TimeStamp",
            "wtc_kWG1TotE_accum",
            "PEP_calculated",
            "ELNX_calculated",
            "ELX_calculated",
            "TurbineNum",
        ]
    ].copy()
    df2_prep = df2_prep.dropna(subset=["TurbineNum"])

    # Rename columns for consistency
    df1_prep.rename(
        columns={
            "Timestamp": "timestamp_aligned",
        },
        inplace=True,
    )

    df2_prep.rename(
        columns={"TimeStamp": "timestamp_aligned"},
        inplace=True,
    )

    # Merge datasets
    print("Merging datasets...")
    merged_inner = pd.merge(
        df1_prep,
        df2_prep,
        on=["TurbineNum", "timestamp_aligned"],
        how="inner",
        suffixes=("_ds1", "_ds2"),
    )

    merged_outer = pd.merge(
        df1_prep,
        df2_prep,
        on=["TurbineNum", "timestamp_aligned"],
        how="outer",
        suffixes=("_ds1", "_ds2"),
    )

    print(f"Inner merge result: {merged_inner.shape}")
    print(f"Outer merge result: {merged_outer.shape}")
    print(f"Dataset 1 original: {df1_prep.shape}")
    print(f"Dataset 2 original: {df2_prep.shape}")

    # Calculate coverage statistics
    coverage_stats = {
        "total_possible_records": len(merged_outer),
        "matched_records": len(merged_inner),
        "coverage_percentage": (len(merged_inner) / len(merged_outer)) * 100
        if len(merged_outer) > 0
        else 0,
        "ds1_only_records": len(
            merged_outer[
                merged_outer["EP"].notna() & merged_outer["wtc_kWG1TotE_accum"].isna()
            ]
        ),
        "ds2_only_records": len(
            merged_outer[
                merged_outer["EP"].isna() & merged_outer["wtc_kWG1TotE_accum"].notna()
            ]
        ),
    }

    print("\nCoverage Statistics:")
    for key, value in coverage_stats.items():
        print(f"  {key}: {value}")

    return merged_inner, merged_outer, coverage_stats


# Perform temporal alignment
if dataset1 is not None and dataset2 is not None:
    merged_data, merged_data_outer, alignment_stats = align_datasets_temporally(
        dataset1, dataset2, column_mapping
    )
else:
    merged_data, merged_data_outer, alignment_stats = None, None, None
    print("Cannot perform temporal alignment - datasets not available")

def calculate_differences(merged_df):
    """Calculate absolute and percentage differences for each mapped column pair"""
    
    if merged_df is None or merged_df.empty:
        print("No merged data available for difference calculations")
        return None
    
    print("Calculating differences between datasets...")
    
    df_diff = merged_df.copy()
    
    # Define column pairs for comparison
    comparison_pairs = {
        'EP': ('EP', 'wtc_kWG1TotE_accum'),
        'PEP': ('PEP', 'PEP_calculated'),
        'EL': ('EL', 'ELNX_calculated'),
        'ELX': ('ELX', 'ELX_calculated')
    }
    
    for metric, (col1, col2) in comparison_pairs.items():
        if col1 in df_diff.columns and col2 in df_diff.columns:
            # Absolute difference
            df_diff[f'{metric}_abs_diff'] = df_diff[col1] - df_diff[col2]
            
            # Percentage difference (avoiding division by zero)
            denominator = df_diff[col2].replace(0, np.nan)
            df_diff[f'{metric}_pct_diff'] = ((df_diff[col1] - df_diff[col2]) / denominator) * 100
            
            # Relative difference (using average as denominator)
            avg_value = (df_diff[col1] + df_diff[col2]) / 2
            avg_value = avg_value.replace(0, np.nan)
            df_diff[f'{metric}_rel_diff'] = ((df_diff[col1] - df_diff[col2]) / avg_value) * 100
            
            print(f"Calculated differences for {metric}")
        else:
            print(f"Warning: Missing columns for {metric} comparison")
    
    return df_diff

def generate_summary_statistics(df_diff):
    """Generate summary statistics per turbine and per timestamp"""
    
    if df_diff is None or df_diff.empty:
        print("No difference data available for summary statistics")
        return None, None
    
    print("Generating summary statistics...")
    
    # Get difference columns
    diff_columns = [col for col in df_diff.columns if '_diff' in col]
    
    # Summary by turbine
    turbine_summary = df_diff.groupby('TurbineNum')[diff_columns].agg([
        'count', 'mean', 'median', 'std', 'min', 'max', 
        lambda x: x.quantile(0.25), lambda x: x.quantile(0.75)
    ]).round(4)
    
    # Flatten column names
    turbine_summary.columns = ['_'.join(col).strip() for col in turbine_summary.columns]
    
    # Summary by timestamp
    timestamp_summary = df_diff.groupby('timestamp_aligned')[diff_columns].agg([
        'count', 'mean', 'median', 'std', 'min', 'max'
    ]).round(4)
    
    # Flatten column names
    timestamp_summary.columns = ['_'.join(col).strip() for col in timestamp_summary.columns]
    
    print(f"Turbine summary shape: {turbine_summary.shape}")
    print(f"Timestamp summary shape: {timestamp_summary.shape}")
    
    return turbine_summary, timestamp_summary

def identify_outliers(df_diff, threshold_std=3):
    """Identify outliers and significant discrepancies"""
    
    if df_diff is None or df_diff.empty:
        print("No difference data available for outlier detection")
        return None
    
    print(f"Identifying outliers using {threshold_std}-sigma threshold...")
    
    outliers_info = {}
    diff_columns = [col for col in df_diff.columns if '_abs_diff' in col]
    
    for col in diff_columns:
        if col in df_diff.columns:
            mean_val = df_diff[col].mean()
            std_val = df_diff[col].std()
            
            # Define outlier thresholds
            upper_threshold = mean_val + threshold_std * std_val
            lower_threshold = mean_val - threshold_std * std_val
            
            # Find outliers
            outliers = df_diff[
                (df_diff[col] > upper_threshold) | (df_diff[col] < lower_threshold)
            ]
            
            outliers_info[col] = {
                'count': len(outliers),
                'percentage': (len(outliers) / len(df_diff)) * 100,
                'mean': mean_val,
                'std': std_val,
                'upper_threshold': upper_threshold,
                'lower_threshold': lower_threshold,
                'outlier_data': outliers[['TurbineNum', 'timestamp_aligned', col]]
            }
            
            print(f"{col}: {len(outliers)} outliers ({(len(outliers) / len(df_diff)) * 100:.2f}%)")
    
    return outliers_info

# Perform comprehensive comparison analysis
if merged_data is not None:
    print("=== Starting Comprehensive Comparison Analysis ===")
    
    # Calculate differences
    differences_data = calculate_differences(merged_data)
    
    # Generate summary statistics
    if differences_data is not None:
        turbine_stats, timestamp_stats = generate_summary_statistics(differences_data)
        
        # Identify outliers
        outliers_analysis = identify_outliers(differences_data)
        
        print("\n=== Analysis Complete ===")
    else:
        turbine_stats, timestamp_stats, outliers_analysis = None, None, None
else:
    differences_data, turbine_stats, timestamp_stats, outliers_analysis = None, None, None, None
    print("No merged data available for comparison analysis")

# Display summary results
if differences_data is not None:
    print("=== COMPARISON RESULTS SUMMARY ===")

    # Overall statistics
    diff_cols = [col for col in differences_data.columns if "_abs_diff" in col]

    print("\nOverall Absolute Differences:")
    for col in diff_cols:
        metric_name = col.replace("_abs_diff", "")
        stats = differences_data[col].describe()
        print(f"\n{metric_name}:")
        print(f"  Mean: {stats['mean']:.4f}")
        print(f"  Std:  {stats['std']:.4f}")
        print(f"  Min:  {stats['min']:.4f}")
        print(f"  Max:  {stats['max']:.4f}")
        print(f"  Median: {stats['50%']:.4f}")

    # Show sample of differences data
    print("\nSample of differences data:")
    display_cols = ["TurbineNum", "timestamp_aligned"] + diff_cols[:4]
    available_cols = [col for col in display_cols if col in differences_data.columns]
    print(differences_data[available_cols].head(10))

    # Correlation analysis
    print("\nCorrelation between original columns:")
    correlation_pairs = [
        ("EP", "wtc_kWG1TotE_accum"),
        ("PEP", "PEP_calculated"),
        ("EL", "EL_calculated"),
        ("ELX", "ELNX_calculated"),
    ]

    for col1, col2 in correlation_pairs:
        if col1 in differences_data.columns and col2 in differences_data.columns:
            corr = differences_data[col1].corr(differences_data[col2])
            print(f"  {col1} vs {col2}: {corr:.4f}")
else:
    print("No comparison results available to display")

def create_comparison_visualizations(df_diff, outliers_info):
    """Create visualizations showing difference distributions and trends"""

    if df_diff is None or df_diff.empty:
        print("No data available for visualizations")
        return

    print("Creating comparison visualizations...")

    # Set up the plotting environment
    plt.rcParams["figure.figsize"] = (15, 10)

    # Get difference columns
    abs_diff_cols = [col for col in df_diff.columns if "_abs_diff" in col]
    pct_diff_cols = [col for col in df_diff.columns if "_pct_diff" in col]

    # 1. Distribution plots for absolute differences
    if abs_diff_cols:
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()

        for i, col in enumerate(abs_diff_cols[:4]):
            if i < len(axes):
                df_diff[col].hist(bins=50, ax=axes[i], alpha=0.7)
                axes[i].set_title(
                    f"Distribution of {col.replace('_abs_diff', '')} Absolute Differences"
                )
                axes[i].set_xlabel("Difference")
                axes[i].set_ylabel("Frequency")
                axes[i].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

    # 2. Box plots for differences by turbine
    if abs_diff_cols and "TurbineNum" in df_diff.columns:
        fig, axes = plt.subplots(2, 2, figsize=(20, 12))
        axes = axes.flatten()

        for i, col in enumerate(abs_diff_cols[:4]):
            if i < len(axes):
                # Sample turbines if too many
                unique_turbines = df_diff["TurbineNum"].unique()
                if len(unique_turbines) > 20:
                    sample_turbines = np.random.choice(
                        unique_turbines, 20, replace=False
                    )
                    plot_data = df_diff[
                        df_diff["TurbineNum"].isin(sample_turbines)
                    ]
                else:
                    plot_data = df_diff

                plot_data.boxplot(column=col, by="TurbineNum", ax=axes[i])
                axes[i].set_title(
                    f"{col.replace('_abs_diff', '')} Differences by Turbine"
                )
                axes[i].set_xlabel("Turbine ID")
                axes[i].set_ylabel("Difference")
                axes[i].tick_params(axis="x", rotation=45)

        plt.tight_layout()
        plt.show()

    # 3. Time series plots
    if abs_diff_cols and "timestamp_aligned" in df_diff.columns:
        fig, axes = plt.subplots(2, 2, figsize=(20, 12))
        axes = axes.flatten()

        for i, col in enumerate(abs_diff_cols[:4]):
            if i < len(axes):
                # Aggregate by timestamp
                time_agg = df_diff.groupby("timestamp_aligned")[col].mean()

                time_agg.plot(ax=axes[i], alpha=0.7)
                axes[i].set_title(
                    f"{col.replace('_abs_diff', '')} Mean Difference Over Time"
                )
                axes[i].set_xlabel("Timestamp")
                axes[i].set_ylabel("Mean Difference")
                axes[i].grid(True, alpha=0.3)
                axes[i].tick_params(axis="x", rotation=45)

        plt.tight_layout()
        plt.show()

    # 4. Correlation heatmap
    comparison_cols = []
    for metric in ["EP", "PEP", "EL", "ELX"]:
        col1 = metric
        col2 = f"{metric}_calculated" if metric != "EP" else "wtc_kWG1TotE_accum"
        if col1 in df_diff.columns and col2 in df_diff.columns:
            comparison_cols.extend([col1, col2])

    if comparison_cols:
        plt.figure(figsize=(12, 10))
        correlation_matrix = df_diff[comparison_cols].corr()
        sns.heatmap(
            correlation_matrix,
            annot=True,
            cmap="coolwarm",
            center=0,
            square=True,
            linewidths=0.5,
        )
        plt.title("Correlation Matrix: Dataset 1 vs Dataset 2 Columns")
        plt.tight_layout()
        plt.show()


# Create visualizations
if differences_data is not None:
    create_comparison_visualizations(differences_data, outliers_analysis)
else:
    print("No data available for visualizations")

def export_analysis_results(
    differences_df,
    turbine_summary,
    timestamp_summary,
    outliers_info,
    alignment_stats,
    output_dir="analysis_results",
):
    """Export all analysis results to CSV files"""

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")

    print(f"Exporting analysis results to {output_dir}/...")

    # Export main differences data
    if differences_df is not None:
        filename = f"{output_dir}/differences_analysis_{timestamp_str}.csv"
        differences_df.to_csv(filename, index=False)
        print(f"Exported differences data: {filename}")

    # Export turbine summary
    if turbine_summary is not None:
        filename = f"{output_dir}/turbine_summary_{timestamp_str}.csv"
        turbine_summary.to_csv(filename)
        print(f"Exported turbine summary: {filename}")

    # Export timestamp summary
    if timestamp_summary is not None:
        filename = f"{output_dir}/timestamp_summary_{timestamp_str}.csv"
        timestamp_summary.to_csv(filename)
        print(f"Exported timestamp summary: {filename}")

    # Export outliers information
    if outliers_info is not None:
        for metric, info in outliers_info.items():
            if not info["outlier_data"].empty:
                filename = f"{output_dir}/outliers_{metric}_{timestamp_str}.csv"
                info["outlier_data"].to_csv(filename, index=False)
                print(f"Exported outliers for {metric}: {filename}")

    # Export alignment statistics
    if alignment_stats is not None:
        filename = f"{output_dir}/alignment_statistics_{timestamp_str}.csv"
        stats_df = pd.DataFrame([alignment_stats])
        stats_df.to_csv(filename, index=False)
        print(f"Exported alignment statistics: {filename}")

    # Create summary report
    report_filename = f"{output_dir}/analysis_summary_report_{timestamp_str}.txt"
    with open(report_filename, "w") as f:
        f.write("Wind Turbine Dataset Comparison Analysis Report\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        if alignment_stats:
            f.write("Alignment Statistics:\n")
            for key, value in alignment_stats.items():
                f.write(f"  {key}: {value}\n")
            f.write("\n")

        if differences_df is not None:
            f.write("Overall Differences Summary:\n")
            diff_cols = [col for col in differences_df.columns if "_abs_diff" in col]
            for col in diff_cols:
                stats = differences_df[col].describe()
                f.write(f"\n{col.replace('_abs_diff', '')}:\n")
                f.write(f"  Mean: {stats['mean']:.4f}\n")
                f.write(f"  Std:  {stats['std']:.4f}\n")
                f.write(f"  Min:  {stats['min']:.4f}\n")
                f.write(f"  Max:  {stats['max']:.4f}\n")

        if outliers_info:
            f.write("\nOutliers Summary:\n")
            for metric, info in outliers_info.items():
                f.write(
                    f"  {metric}: {info['count']} outliers ({info['percentage']:.2f}%)\n"
                )

    print(f"Exported summary report: {report_filename}")
    print(f"\nAll analysis results exported to: {output_dir}/")


# Export results
if any(
    [
        differences_data,
        turbine_stats,
        timestamp_stats,
        outliers_analysis,
        alignment_stats,
    ]
):
    export_analysis_results(
        differences_data,
        turbine_stats,
        timestamp_stats,
        outliers_analysis,
        alignment_stats,
    )
else:
    print("No analysis results available for export")

def perform_data_quality_checks(df1, df2, merged_df):
    """Perform comprehensive data quality checks"""

    print("=== DATA QUALITY VALIDATION REPORT ===")

    quality_report = {}

    # Dataset 1 quality checks
    if df1 is not None:
        print("\nDataset 1 (CSV) Quality Checks:")

        # Check for duplicates
        duplicates = df1.duplicated(subset=["Turbine Name", "Timestamp"]).sum()
        print(f"  Duplicate records: {duplicates}")

        # Check for missing timestamps
        missing_timestamps = df1["Timestamp"].isnull().sum()
        print(f"  Missing timestamps: {missing_timestamps}")

        # Check for negative values in energy columns
        energy_cols = ["EP", "PEP", "EL", "ELX"]
        for col in energy_cols:
            if col in df1.columns:
                negative_count = (df1[col] < 0).sum()
                print(f"  Negative values in {col}: {negative_count}")

        quality_report["dataset1"] = {
            "duplicates": duplicates,
            "missing_timestamps": missing_timestamps,
            "total_records": len(df1),
        }

    # Dataset 2 quality checks
    if df2 is not None:
        print("\nDataset 2 (Pickle) Quality Checks:")

        # Check for duplicates
        duplicates = df2.duplicated(subset=["StationId", "TimeStamp"]).sum()
        print(f"  Duplicate records: {duplicates}")

        # Check for missing timestamps
        missing_timestamps = df2["TimeStamp"].isnull().sum()
        print(f"  Missing timestamps: {missing_timestamps}")

        # Check for missing station IDs
        missing_stations = df2["StationId"].isnull().sum()
        print(f"  Missing station IDs: {missing_stations}")

        quality_report["dataset2"] = {
            "duplicates": duplicates,
            "missing_timestamps": missing_timestamps,
            "missing_stations": missing_stations,
            "total_records": len(df2),
        }

    # Merged data quality checks
    if merged_df is not None:
        print("\nMerged Data Quality Checks:")

        # Check data completeness
        total_possible = len(merged_df)
        complete_records = merged_df.dropna().shape[0]
        completeness = (
            (complete_records / total_possible) * 100 if total_possible > 0 else 0
        )

        print(f"  Data completeness: {completeness:.2f}%")
        print(f"  Complete records: {complete_records}/{total_possible}")

        # Check for extreme differences
        diff_cols = [col for col in merged_df.columns if "_abs_diff" in col]
        for col in diff_cols:
            if col in merged_df.columns:
                extreme_count = (abs(merged_df[col]) > merged_df[col].std() * 5).sum()
                print(f"  Extreme differences in {col}: {extreme_count}")

        quality_report["merged"] = {
            "completeness_pct": completeness,
            "complete_records": complete_records,
            "total_records": total_possible,
        }

    print("\n=== DATA QUALITY VALIDATION COMPLETE ===")
    return quality_report


# Perform data quality checks
quality_results = perform_data_quality_checks(
    dataset1, dataset2, differences_data
)

print("\n=== ANALYSIS COMPLETE ===")
print("\nNext Steps:")
print("1. Review the exported CSV files for detailed analysis")
print("2. Investigate any significant outliers identified")
print("3. Consider additional data validation if quality issues are found")
print("4. Use the visualizations to understand patterns in the differences")